package com.zenyte.game.content.quests;

import com.google.gson.annotations.Expose;

import com.zenyte.game.content.quests.quests.*;
import com.zenyte.game.util.BitUtils;

import com.zenyte.game.util.Utils;
import com.zenyte.game.world.entity.player.Player;

import com.zenyte.plugins.Listener;
import com.zenyte.plugins.ListenerType;
import java.util.*;
import java.util.function.Predicate;

/**
 * <AUTHOR> (Discord: imslickk) - With assistance from Augment AI
 */
@SuppressWarnings("unused")
public final class QuestManager {
    private transient Player player;
    @Expose
    private final Map<String, String> currentQuestObjectives = new HashMap<>();

    @Expose
    private final Map<String, Integer> questObjectiveProgress = new HashMap<>();



    public QuestManager(final Player player) {
        this.player = player;
    }

    public void initialize(final Player player, final Player parser) {
        this.player = player;
        final QuestManager parserData = parser.getQuestManager();
        if (parserData == null) {
            return;
        }
        currentQuestObjectives.putAll(parserData.currentQuestObjectives);
        questObjectiveProgress.putAll(parserData.questObjectiveProgress);
    }

    /**
     * Gets the current progress of a quest objective in either a total value or a mask.
     *
     * @param quest the quest objective.
     * @return current progress within the objective.
     */
    public int getProgress(final Quest quest) {
        String questName = quest.title();
        String currentObjective = currentQuestObjectives.get(questName);

        // If no current objective stored, quest hasn't been started
        if (currentObjective == null) {
            return 0;
        }

        // If this is the current objective, return stored progress
        if (currentObjective.equals(quest.name())) {
            return questObjectiveProgress.getOrDefault(getProgressKey(quest), 0);
        }

        // Check if this objective comes before the current objective in the quest sequence
        if (isObjectiveBeforeCurrent(quest, currentObjective)) {
            return quest.objectiveLength(); // Completed
        }

        // This objective comes after the current objective, so it's not started
        return 0;
    }

    /**
     * Generates a unique key for storing objective progress
     */
    private String getProgressKey(Quest quest) {
        return quest.title() + ":" + quest.name();
    }

    /**
     * Checks if a quest objective comes before the current objective in the quest sequence
     */
    private boolean isObjectiveBeforeCurrent(Quest quest, String currentObjectiveName) {
        // Get all quest objectives for this quest
        Quest[] allObjectives = getQuestObjectives(quest.title());

        boolean foundQuest = false;
        for (Quest objective : allObjectives) {
            if (objective.name().equals(quest.name())) {
                foundQuest = true;
            } else if (objective.name().equals(currentObjectiveName)) {
                return foundQuest; // If we found the quest before finding current objective
            }
        }

        return false;
    }

    /**
     * Gets all quest objectives for a given quest title
     */
    private Quest[] getQuestObjectives(String questTitle) {
        for (final Quest[] questSet : ALL_QUESTS) {
            if (questSet.length > 0 && questSet[0].title().equals(questTitle)) {
                return questSet;
            }
        }
        return new Quest[0];
    }

    /**
     * Refreshes the quest progress (simplified - no GUI components).
     *
     * @param quest the quest to refresh.
     */
    public void refresh(final Quest quest) {
        // Simplified refresh - no varbit/GUI updates needed
        // Quest progress is tracked through the map and dialogue system
    }

    /**
     * Gets a quest objective that has dialogue for the specified NPC.
     *
     * @param npcId the NPC ID
     * @param player the player (to check quest progress)
     * @return the appropriate quest objective for dialogue, or null if none found
     */
    public static Quest getQuestObjectiveForNpc(final int npcId, final Player player) {
        final QuestManager manager = player.getQuestManager();

        for (final Quest[] questSet : ALL_QUESTS) {
            for (final Quest quest : questSet) {
                // Check if this quest objective has dialogue for this NPC
                if (quest.getDialogueNpc() == npcId) {
                    // Return the appropriate quest objective based on progress
                    if (!manager.isCompleted(quest)) {
                        return quest;
                    }
                }
            }
        }
        return null;
    }



    public static final Quest[][] ALL_QUESTS = new Quest[][] {
        AdventurersPath.VALUES
    };

    @Listener(type = ListenerType.LOBBY_CLOSE)
    private static void onLogin(final Player player) {
        final QuestManager manager = player.getQuestManager();
        for (final Quest[] quest : ALL_QUESTS) {
            manager.refresh(quest[0]);
        }
    }

    public void finish(final Quest quest) {
        update(quest, quest.objectiveLength(), true);
        refresh(quest);
    }

    public void setFinished(final Quest quest) {
        // Remove the quest from current objectives (marks as completed)
        currentQuestObjectives.remove(quest.title());

        // Clear all progress data for this quest since it's completed
        Quest[] allObjectives = getQuestObjectives(quest.title());
        for (Quest objective : allObjectives) {
            questObjectiveProgress.remove(getProgressKey(objective));
        }
    }

    public void reset(final Quest quest) {
        // Reset quest by removing it from current objectives
        currentQuestObjectives.remove(quest.title());

        // Clear all progress data for this quest
        Quest[] allObjectives = getQuestObjectives(quest.title());
        for (Quest objective : allObjectives) {
            questObjectiveProgress.remove(getProgressKey(objective));
        }

        refresh(quest);
    }

    public void update(final Quest quest) {
        update(quest, 1, false);
    }

    public void update(final Quest quest, final int amount) {
        update(quest, amount, false);
    }

    /**
     * Simplified quest update method that handles rewards automatically.
     * Use this method for easy quest progression with a single line of code.
     *
     * @param questClass the quest class (e.g., AdventurersPath.class)
     * @param objectiveName the objective name (e.g., "COMPLETE_SLAYER_TASK_TURAEL")
     * @param objectiveLength the objective length amount (optional, defaults to 1)
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public static void updateQuest(Player player, Class<? extends Enum<? extends Quest>> questClass, String objectiveName, int objectiveLength) {
        try {
            // Get the quest enum constant by name using reflection
            Class enumClass = questClass;
            Enum questEnum = Enum.valueOf(enumClass, objectiveName);
            Quest quest = (Quest) questEnum;

            QuestManager manager = player.getQuestManager();

            // Check if quest objective is already completed
            if (manager.isCompleted(quest)) {
                return;
            }

            // Update quest progress
            manager.update(quest, objectiveLength, false);

            // If quest objective is now completed, give rewards
            if (manager.isCompleted(quest)) {
                quest.giveRewards(player);
            }

        } catch (IllegalArgumentException e) {
            // Quest objective not found
            player.sendMessage("Quest objective not found: " + objectiveName);
        } catch (Exception e) {
            // Other errors
            player.sendMessage("Error updating quest: " + e.getMessage());
        }
    }

    /**
     * Simplified quest update method with default objective length of 1.
     */
    public static void updateQuest(Player player, Class<? extends Enum<? extends Quest>> questClass, String objectiveName) {
        updateQuest(player, questClass, objectiveName, 1);
    }

    /**
     * Updates the requested quest objective if it hasn't already been completed.
     *
     * @param quest the quest objective to update.
     * @param amount the amount to add to current progress, or the flag to append if the quest is flag-based.
     * @param force whether to force the update regardless of completion status.
     */
    public void update(final Quest quest, final int amount, final boolean force) {
        final int progress = getProgress(quest);
        final Predicate<Player> predicate = quest.predicate();

        if (!force && (progress >= quest.objectiveLength() || predicate != null && !predicate.test(player))) {
            return;
        }

        int newValue;
        if (quest.flagging()) {
            newValue = progress | amount;
            if (!force && newValue == progress) {
                return;
            }
        } else {
            newValue = progress + amount;
        }

        // Cap the value at the objective length
        newValue = Math.min(newValue, quest.objectiveLength());

        // Store the progress for this objective
        questObjectiveProgress.put(getProgressKey(quest), newValue);

        // Send progress messages
        if (newValue < quest.objectiveLength()) {
            if (quest.flagging()) {
                final int bitsFlagged = BitUtils.getAmountOfBitsFlagged(newValue);
                player.sendMessage("<col=0040ff>Quest Progress - " + bitsFlagged + "/" + quest.objectiveLength() + " steps completed.");
            } else {
                player.sendMessage("<col=0040ff>Quest Progress - " + newValue + "/" + quest.objectiveLength() + " steps completed.");
            }
        } else {
            // Get the objective description for a better completion message
            final String objectiveDescription = String.join(" ", quest.objectiveDescription());
            player.sendMessage("<col=dc143c>Well done! You have completed the objective: " + objectiveDescription + " Your Quest Log has been updated.");

            // Give rewards when objective is completed
            quest.giveRewards(player);

            // When an objective is completed, move to the next objective
            moveToNextObjective(quest);
        }

        refresh(quest);
    }

    /**
     * Moves to the next objective in the quest sequence when current objective is completed
     */
    private void moveToNextObjective(Quest completedQuest) {
        Quest[] allObjectives = getQuestObjectives(completedQuest.title());

        // Find the next objective after the completed one
        for (int i = 0; i < allObjectives.length - 1; i++) {
            if (allObjectives[i].name().equals(completedQuest.name())) {
                // Set the next objective as current
                Quest nextObjective = allObjectives[i + 1];
                currentQuestObjectives.put(completedQuest.title(), nextObjective.name());

                // Initialize progress for the new objective
                questObjectiveProgress.put(getProgressKey(nextObjective), 0);
                return;
            }
        }

        // If this was the last objective, remove the quest from current objectives (completed)
        currentQuestObjectives.remove(completedQuest.title());
    }

    public boolean isCompleted(Quest quest) {
        return getProgress(quest) == quest.objectiveLength();
    }

    /**
     * Gets all current quest objectives for debugging/display
     * @return map of quest names to current objective names
     */
    public Map<String, String> getCurrentQuestObjectives() {
        return new HashMap<>(currentQuestObjectives);
    }

    /**
     * Gets all quest objective progress for debugging/display
     * @return map of progress keys to progress values
     */
    public Map<String, Integer> getQuestObjectiveProgress() {
        return new HashMap<>(questObjectiveProgress);
    }

    /**
     * Gets the current objective for a specific quest
     * @param questTitle the quest title
     * @return current objective name or null if quest not started/completed
     */
    public String getCurrentObjective(String questTitle) {
        return currentQuestObjectives.get(questTitle);
    }

    /**
     * Starts a quest by setting its first objective as current
     * @param quest the first quest objective
     */
    public void startQuest(Quest quest) {
        currentQuestObjectives.put(quest.title(), quest.name());
        // Initialize progress for the starting objective
        questObjectiveProgress.put(getProgressKey(quest), 0);
    }

    public boolean isAllCompleted() {
        for (final Quest[] set : ALL_QUESTS) {
            for (final Quest quest : set) {
                if (!isCompleted(quest)) {
                    return false;
                }
            }
        }
        return true;
    }

    public boolean isAllCompleted(QuestStage stage) {
        for (final Quest[] set : ALL_QUESTS) {
            for (final Quest quest : set) {
                if (quest.stage() == stage && !isCompleted(quest)) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * Gets all NPCs involved in all quests.
     * This method collects NPCs from all quest classes.
     *
     * @return array of all NPC IDs used by all quests
     */
    public static int[] getNpcs() {
        Set<Integer> allNpcs = new HashSet<>();

        // Iterate through all quest sets
        for (final Quest[] questSet : ALL_QUESTS) {
            // For each quest set, we only need to check the first quest since
            // getQuestNpcs() is implemented at the quest class level
            if (questSet.length > 0) {
                Quest firstQuest = questSet[0];
                int[] questNpcs = firstQuest.getQuestNpcs();
                for (int npcId : questNpcs) {
                    allNpcs.add(npcId);
                }
            }
        }

        // Convert Set to array
        return allNpcs.stream().mapToInt(Integer::intValue).toArray();
    }

}
